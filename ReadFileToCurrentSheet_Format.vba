Sub ReadFileToCurrentSheet()
    Dim mainSheet As Worksheet, currentSheet As Worksheet
    Dim dirPath As String, filePath As String, longFilePath As String
    Dim fileNum As Integer, i As Integer, j <PERSON> Integer, lineCount As Integer
    Dim lineData As String
    Dim rowData() As String, cellData() As String
    Dim dataArray() As Variant ' 用于批量写入的数组
    Dim lastRow As Long, maxCols As Integer, arrRow As Integer
    Dim errorMsg As String, fileSize As Long
    Dim startTime As Double ' 添加计时变量
    Dim targetRange As Range ' 目标区域
    
    On Error GoTo ErrorHandler
    
    startTime = Timer ' 开始计时
    
    ' 设置当前工作表
    Set currentSheet = ActiveSheet
    
    ' 检查是否为无效工作表
    If currentSheet.Name = "主控文件" Then
        MsgBox "不可在【主控文件】工作表中执行此操作", vbExclamation
        Exit Sub
    End If
    
    ' 验证主控文件
    Set mainSheet = ThisWorkbook.Sheets("主控文件")
    If mainSheet Is Nothing Then
        MsgBox "未找到【主控文件】工作表", vbCritical
        Exit Sub
    End If
    
    ' 获取并验证目录路径
    dirPath = mainSheet.Cells(2, 4).Value
    If dirPath = "" Then
        MsgBox "【主控文件】D2单元格未填写目录路径", vbCritical
        Exit Sub
    End If
    If Right(dirPath, 1) <> "\" Then dirPath = dirPath & "\"
    If Dir(dirPath, vbDirectory) = "" Then
        MsgBox "目录路径无效：" & dirPath, vbCritical
        Exit Sub
    End If
    
    ' 构建文件路径
    filePath = dirPath & currentSheet.Name
    longFilePath = "\\?\" & filePath
    If Dir(filePath) = "" Then
        MsgBox "未找到文件：" & filePath, vbCritical
        Exit Sub
    End If
    
    ' 清空旧数据（第3行及以下）
    lastRow = currentSheet.Cells(currentSheet.Rows.Count, 1).End(xlUp).Row
    If lastRow >= 3 Then
        currentSheet.Rows("3:" & lastRow).ClearContents
    End If
    
    ' 读取文件并统计行列数（第一遍扫描）
    fileNum = FreeFile
    Open longFilePath For Input As #fileNum
        fileSize = LOF(fileNum)
        If fileSize = 0 Then
            Close #fileNum
            MsgBox "文件为空：" & filePath, vbExclamation
            Exit Sub
        End If
        
        ' 第一遍：统计行数和最大列数，用于初始化数组
        lineCount = 0
        maxCols = 0
        Do While Not EOF(fileNum)
            Line Input #fileNum, lineData
            If Trim(lineData) <> "" Then
                lineCount = lineCount + 1
                cellData = Split(lineData, Chr(9))
                If UBound(cellData) + 1 > maxCols Then
                    maxCols = UBound(cellData) + 1 ' 记录最大列数
                End If
            End If
        Loop
        Close #fileNum
        
        ' 初始化批量写入数组（大小为实际需要的行列数）
        ReDim dataArray(1 To lineCount, 1 To maxCols)
        arrRow = 0
        
        ' 第二遍：读取内容到数组（内存操作，速度极快）
        fileNum = FreeFile
        Open longFilePath For Input As #fileNum
            Do While Not EOF(fileNum)
                Line Input #fileNum, lineData
                If Trim(lineData) <> "" Then
                    arrRow = arrRow + 1
                    cellData = Split(lineData, Chr(9))
                    ' 填充当前行数据到数组 - 保持原始数据
                    For j = 0 To UBound(cellData)
                        dataArray(arrRow, j + 1) = cellData(j)
                    Next j
                End If
            Loop
        Close #fileNum
    
    ' 关键优化：关闭所有Excel界面更新
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual ' 关闭自动计算
    Application.EnableEvents = False ' 关闭事件触发
    
    ' 设置目标区域并先格式化为文本格式
    If lineCount > 0 Then
        Set targetRange = currentSheet.Cells(3, 1).Resize(lineCount, maxCols)
        
        ' 关键修复：先将目标区域格式设置为文本
        targetRange.NumberFormat = "@"
        
        ' 批量写入数据（一次写入比逐单元格写入快10-100倍）
        targetRange.Value = dataArray
    End If
    
    ' 恢复Excel设置
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True
    
    MsgBox "数据导入完成！" & vbCrLf & _
           "文件：" & filePath & vbCrLf & _
           "数据量：" & lineCount & "行，" & maxCols & "列" & vbCrLf & _
           "耗时优化：约" & Format(Timer - startTime, "0.00") & "秒", vbInformation
    
    Exit Sub
    
ErrorHandler:
    ' 出错时强制恢复设置
    On Error Resume Next
    Close #fileNum
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True
    
    errorMsg = "操作失败！" & vbCrLf & _
               "错误：" & Err.Number & " - " & Err.Description
    MsgBox errorMsg, vbCritical
End Sub
